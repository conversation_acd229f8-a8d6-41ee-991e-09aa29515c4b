package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.DingmsgPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.HttpUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.HttpRequestUtil;
import inks.service.sa.uts.domain.pojo.UtsWxemsgPojo;
import inks.service.sa.uts.service.UtsWxemsgService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.VelocityUtils;
import inks.service.sa.uts.utils.wxutils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * 企业微信信息(Uts_WxeMsg)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-13 13:48:09
 */
@RestController
@RequestMapping("S34M01B1")
@Api(tags = "S34M01B1:企业微信信息")
public class S34M01B1Controller extends UtsWxemsgController {

    private final static Logger logger = LoggerFactory.getLogger(S34M01B1Controller.class);
    @Resource
    private UtsWxemsgService utsWxemsgService;
    @Resource
    private SaConfigService saConfigService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 企业微信第三方应用发送信息 通过MsgCode", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendTextMsg", method = RequestMethod.POST)
    public R<String> sendTextMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        try {
            if (StringUtils.isBlank(tid)) {
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            // 获得用户数据 DingmsgPojo是工具类，接参通用于钉钉和企业微信
            DingmsgPojo wxemsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
            String msgCode = wxemsgPojoReq.getMsgcode();
            String msgText = wxemsgPojoReq.getMsgtext();//最终发送信息内容
            UtsWxemsgPojo utsWxmsgDB = this.utsWxemsgService.getEntityByMsgCode(msgCode, tid);
            if (utsWxmsgDB == null) {
                PrintColor.lv("wxe：未找到对应的消息模板，跳出");
                return R.fail(wxemsgPojoReq.getMsgcode() + "信息模板未找到");
            }


            // 使用Set来保证唯一性，避免重复
            Set<String> userListSet = new HashSet<>();

// 1. 先处理数据库中的用户列表
            String userListDb = utsWxmsgDB.getUserlist();
            if (StringUtils.isNotBlank(userListDb)) {
                String[] dbUsers = userListDb.split(",");
                Collections.addAll(userListSet, dbUsers);
            }

// 2. 追加传入的用户列表
            String userListReq = wxemsgPojoReq.getUserlist();
            if (StringUtils.isNotBlank(userListReq)) {
                String[] reqUsers = userListReq.split(",");
                Collections.addAll(userListSet, reqUsers);
            }
            PrintColor.zi("发送人列表1：本次前端传入的userListReq:" + userListReq + ", 消息模板数据库已有的.UserList:" + userListDb);

// 3. 处理 ObjJson 数组，加入微信用户ID
            String objJsonStr = utsWxmsgDB.getObjjson();
            if (StringUtils.isNotBlank(objJsonStr)) {
                JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
                for (int i = 0; i < objJsonArray.size(); i++) {
                    JSONObject obj = objJsonArray.getJSONObject(i);
                    String oms_userid = obj.getString("objcode");

                    // 如果是VM模板，进行渲染
                    if (oms_userid.trim().startsWith("$")) {
                        oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                    }

                    // 根据 oms_userid 查询对应的微信用户ID
                    String wx_userid = utsWxemsgService.getWxeUserIdByOmsUserid(oms_userid, tid);

                    // 如果查到了微信用户ID，添加到发送人列表
                    if (StringUtils.isNotBlank(wx_userid)) {
                        userListSet.add(wx_userid); // 添加到Set中，避免重复
                    }
                }
            }
            PrintColor.zi("发送人列表2：消息模板数据库.ObjJson:" + objJsonStr);

// 将Set转换为String，逗号分隔
            String finalUserList = String.join(",", userListSet);

// 打印最终的发送人列表
            PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserList);

            // 是否转换信息Vm模版
            String msgVM = utsWxmsgDB.getMsgtemplate();
            if (StringUtils.isNotBlank(msgVM)) {
                // 根据模版和内容 返回填充后数据
                msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);
                PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
            }

            logger.info("开始免登 uuid：" + finalUserList);
            logger.info("开始免登 tid：" + tid);

            logger.info("------开始企微发信息,获取参数-------");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
            // 注意 access_token的获取只需要corpId和agentSecret
            // agentId是用来指定由哪个应用来发送消息的
            QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
            logger.info("企业微信免登 agentID:" + QyWeChat.agentId);

            // 获取access_token，需要corpid、corpsecret
            String token = QyWeChatUtils.refreshToken("agentToken");
            logger.info("企业微信免登 Token:" + token);

            String url = QyWeChat.message.replace("{access_token}", token);
            WxeMessagePojo wxeMessagePojo = new WxeMessagePojo();
            wxeMessagePojo.setAgentid(QyWeChat.agentId);
            //wxeMessagePojo.setAgentid(1000013);// 应用名：JustAuth1000013
            wxeMessagePojo.setTouser(finalUserList.replace(",", "|"));
            wxeMessagePojo.setSafe(0);
            wxeMessagePojo.setMsgtype("text");
            WxeTextPojo wxeTextPojo = new WxeTextPojo();
            wxeTextPojo.setContent(msgText);
            wxeMessagePojo.setText(wxeTextPojo);
            JSONObject jsonObject = SendRequest.sendPost(url, JSONObject.toJSONString(wxeMessagePojo));
            if ("ok".equals(jsonObject.get("errmsg"))) {
                return R.ok(jsonObject.get("errmsg").toString());
            } else {
                logger.error("企微消息发送失败:" + jsonObject.get("errmsg"));
                return R.fail(jsonObject.get("errmsg").toString());
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // 官方文档：https://developer.work.weixin.qq.com/document/path/91982
    // 举例： b8租户，销售订单审批模板templateId：C4UAWELPHbJDV3qDRKfmKNZEuHQqB1EcLAGeRHM5Z
    @ApiOperation(value = "根据templateId获取企业微信表单schema信息", notes = "通过templateId获取企业微信审批模板表单schema信息", produces = "application/json")
    @RequestMapping(value = "/getFormSchemaByTemplateId", method = RequestMethod.GET)
    public R<Map<String, Object>> getFormSchemaByTemplateId(@RequestParam String templateId) throws ApiException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        logger.info("------开始企业微信获取表单schema信息,获取参数-------");

        // 获取配置
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信 corpId: {}", QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信 agentSecret: {}", QyWeChat.agentSecret);

        // 获取access_token
        String accessToken = QyWeChatUtils.refreshToken("agentToken");
        if (accessToken == null) {
            throw new ApiException("获取accessToken失败");
        }
        logger.info("企业微信 Token: {}", accessToken);

        // 请求URL
        //设置参数template_id
        String url = QyWeChat.templateDetailURL.replace("{access_token}", accessToken);
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("template_id", templateId);

        // 发送请求
        try {
            JSONObject response = HttpRequestUtil.sendPost(url, requestBody.toJSONString());
            if (response == null || response.getInteger("errcode") != 0) {
                String errmsg = response != null ? response.getString("errmsg") : "响应为空";
                logger.error("获取模板详情失败: {}", errmsg);
                throw new ApiException("获取模板详情失败: " + errmsg);
            }

            // 提取模板 schema 信息
            // {
            //    "errcode": 0,
            //    "errmsg": "ok",
            //    "template_names": { 模版名称
            //    "template_content": { 模版内容
            JSONObject templateDetail = response.getJSONObject("template_content");
            return R.ok(response);
        } catch (Exception e) {
            logger.error("企业微信审批API调用异常", e);
            throw new ApiException("企业微信审批API调用异常");
        }
    }


}
